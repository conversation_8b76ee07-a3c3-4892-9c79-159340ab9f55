<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Roulette;
use App\Models\RouletteBets;
use App\Models\User;
use App\Models\Settings;
use App\Models\Profit;

class RouletteController extends Controller
{
    /**
     * Place a bet on the roulette table
     */
    public function bet(Request $request)
    {
        $settings = Settings::where('id', 1)->first();
        // Guest mode - create virtual user if not authenticated
        if (!Auth::check()) {
            $guestUser = new \App\Models\User();
            $guestUser->id = 999999;
            $guestUser->username = 'Guest_' . uniqid();
            $guestUser->balance = 10000.00;
            $guestUser->is_guest = true;
            Auth::setUser($guestUser);
        }
        
        if ($settings && isset($settings->roulette_enabled) && $settings->roulette_enabled == 1) {
            return response()->json(['type' => 'error', 'msg' => 'В данный момент режим недоступен!']);
        }

        $betType = $request->bet_type;
        $betValue = $request->bet_value;
        $amount = $request->amount;
        $user = Auth::user();

        // Validation
        if ($amount < 1 || !is_numeric($amount)) {
            return response()->json(['type' => 'error', 'msg' => 'Минимальная сумма ставки - 1 рубль!']);
        }
        
        if ($amount > $user->balance) {
            return response()->json(['type' => 'error', 'msg' => 'Недостаточно средств']);
        }

        // Get or create current game
        $game = Roulette::where('status', 'betting')->first();
        if (!$game) {
            $game = Roulette::create([
                'winning_number' => 0,
                'status' => 'betting'
            ]);
        }

        // Check if user already has too many bets
        $userBetsCount = RouletteBets::where('user_id', $user->id)
                                   ->where('game_id', $game->id)
                                   ->count();
        
        if ($userBetsCount >= 10) {
            return response()->json(['type' => 'error', 'msg' => 'Максимум 10 ставок на игру']);
        }

        DB::beginTransaction();
        try {
            // Deduct bet from user balance
            $user->balance -= $amount;
            $user->save();

            // Get payout multiplier
            $payoutMultiplier = Roulette::getPayoutMultiplier($betType);

            // Create bet record
            $bet = RouletteBets::create([
                'user_id' => $user->id,
                'game_id' => $game->id,
                'bet_type' => $betType,
                'bet_value' => $betValue,
                'amount' => $amount,
                'payout_multiplier' => $payoutMultiplier
            ]);

            // Update game total bets
            $game->total_bets += $amount;
            $game->save();

            DB::commit();

            return response()->json([
                'type' => 'success',
                'msg' => 'Ставка принята!',
                'bet' => $bet,
                'balance' => $user->balance,
                'game_id' => $game->id
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['type' => 'error', 'msg' => 'Ошибка при размещении ставки']);
        }
    }

    /**
     * Spin the roulette wheel (admin function)
     */
    public function spin(Request $request)
    {
        if (!Auth::check() || Auth::user()->admin != 1) {
            return response()->json(['type' => 'error', 'msg' => 'Недостаточно прав']);
        }

        $game = Roulette::where('status', 'betting')->first();
        if (!$game) {
            return response()->json(['type' => 'error', 'msg' => 'Нет активной игры']);
        }

        // Generate winning number (0-36)
        $winningNumber = rand(0, 36);
        
        // Update game status
        $game->winning_number = $winningNumber;
        $game->status = 'spinning';
        $game->save();

        // Process all bets
        $this->processBets($game, $winningNumber);

        // Mark game as finished
        $game->status = 'finished';
        $game->save();

        return response()->json([
            'type' => 'success',
            'winning_number' => $winningNumber,
            'color' => Roulette::getNumberColor($winningNumber),
            'game_id' => $game->id
        ]);
    }

    /**
     * Process all bets for a game
     */
    private function processBets($game, $winningNumber)
    {
        $bets = RouletteBets::where('game_id', $game->id)->get();
        $totalProfit = 0;

        DB::beginTransaction();
        try {
            foreach ($bets as $bet) {
                $isWinner = Roulette::checkBetWin($bet->bet_type, $bet->bet_value, $winningNumber);
                
                if ($isWinner) {
                    $winAmount = $bet->amount * ($bet->payout_multiplier + 1); // +1 to include original bet
                    $bet->win = $winAmount;
                    $bet->is_winner = true;
                    
                    // Add winnings to user balance
                    $user = User::find($bet->user_id);
                    if ($user) {
                        $user->balance += $winAmount;
                        $user->save();
                    }
                    
                    $totalProfit -= ($winAmount - $bet->amount); // Subtract house loss
                } else {
                    $bet->is_winner = false;
                    $totalProfit += $bet->amount; // Add house win
                }
                
                $bet->save();
            }

            // Record profit
            Profit::create([
                'game' => 'roulette',
                'sum' => $totalProfit
            ]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
        }
    }

    /**
     * Get current game status
     */
    public function status(Request $request)
    {
        $game = Roulette::where('status', 'betting')->first();
        
        if (!$game) {
            return response()->json([
                'type' => 'success',
                'game' => null,
                'message' => 'Нет активной игры'
            ]);
        }

        $bets = RouletteBets::where('game_id', $game->id)
                           ->with('user:id,username,avatar')
                           ->get();

        return response()->json([
            'type' => 'success',
            'game' => $game,
            'bets' => $bets,
            'total_bets' => $game->total_bets
        ]);
    }

    /**
     * Get recent game history
     */
    public function history(Request $request)
    {
        $history = Roulette::where('status', 'finished')
                          ->orderBy('id', 'desc')
                          ->limit(20)
                          ->get(['id', 'winning_number', 'created_at']);

        $historyWithColors = $history->map(function ($game) {
            return [
                'id' => $game->id,
                'number' => $game->winning_number,
                'color' => Roulette::getNumberColor($game->winning_number),
                'created_at' => $game->created_at
            ];
        });

        return response()->json([
            'type' => 'success',
            'history' => $historyWithColors
        ]);
    }

    /**
     * Get user's bets for current game
     */
    public function userBets(Request $request)
    {
        // Guest mode - create virtual user if not authenticated
        if (!Auth::check()) {
            $guestUser = new \App\Models\User();
            $guestUser->id = 999999;
            $guestUser->username = 'Guest_' . uniqid();
            $guestUser->balance = 10000.00;
            $guestUser->is_guest = true;
            Auth::setUser($guestUser);
        }

        $game = Roulette::where('status', 'betting')->first();
        if (!$game) {
            return response()->json(['type' => 'success', 'bets' => []]);
        }

        $bets = RouletteBets::where('user_id', Auth::user()->id)
                           ->where('game_id', $game->id)
                           ->get();

        return response()->json([
            'type' => 'success',
            'bets' => $bets
        ]);
    }
}

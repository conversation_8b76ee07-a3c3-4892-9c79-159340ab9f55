# Casino Website Setup Guide

This guide will help you set up and run the casino website with all games functioning properly.

## Prerequisites

- PHP 8.1 or higher
- Node.js 16 or higher
- MySQL 5.7 or higher
- Redis server
- Composer
- NPM or Yarn

## Installation Steps

### 1. Environment Setup

1. Copy the environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` file with your database credentials:
   ```
   DB_DATABASE=your_database_name
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

3. Generate application key:
   ```bash
   php artisan key:generate
   ```

### 2. Database Setup

1. Create a MySQL database
2. Run migrations:
   ```bash
   php artisan migrate
   ```

3. Seed the database (optional):
   ```bash
   php artisan db:seed
   ```

### 3. Install Dependencies

1. Install PHP dependencies:
   ```bash
   composer install
   ```

2. Install Node.js dependencies:
   ```bash
   npm install
   ```

### 4. Build Assets

For development:
```bash
npm run dev
```

For production:
```bash
npm run production
```

### 5. Start Services

#### Option A: Development Mode

1. Start Laravel development server:
   ```bash
   php artisan serve
   ```

2. Start Redis server:
   ```bash
   redis-server
   ```

3. Start Socket.io development server:
   ```bash
   node server/dev-server.js
   ```

#### Option B: Production Mode

1. Configure your web server (Apache/Nginx) to serve the `public` directory

2. Start Redis server

3. Start Socket.io server:
   ```bash
   node server/app.js
   ```

## Game Configuration

### Enable/Disable Games

Edit your `.env` file to enable or disable specific games:

```
DICE_ENABLED=0    # 0 = enabled, 1 = disabled
MINES_ENABLED=0
CRASH_ENABLED=0
WHEEL_ENABLED=0
```

### Socket.io Configuration

The socket server will automatically detect if SSL certificates are available and use HTTPS if possible, otherwise it will fallback to HTTP.

For production with SSL:
1. Ensure SSL certificates are in place
2. Update the SSL paths in `.env`:
   ```
   SSL_KEY_PATH=/path/to/your/privkey.pem
   SSL_CERT_PATH=/path/to/your/fullchain.pem
   ```

## Troubleshooting

### Common Issues

1. **Games not loading**: 
   - Check if Socket.io server is running
   - Verify Redis connection
   - Check browser console for errors

2. **Socket connection failed**:
   - Ensure port 8443 is open
   - Check firewall settings
   - Verify the socket URL in browser console

3. **Slots not working**:
   - Check external API connectivity
   - Verify domain configuration in SlotsController

4. **Database errors**:
   - Verify database credentials in `.env`
   - Ensure database exists
   - Run migrations

### Development vs Production

- **Development**: Use `server/dev-server.js` for simplified testing
- **Production**: Use `server/app.js` with proper SSL configuration

## Language Settings

The website is now configured for English by default. All Russian text has been replaced with English equivalents.

## Security Notes

- Change default passwords
- Configure proper CORS settings for production
- Use HTTPS in production
- Secure your Redis instance
- Regular security updates

## Support

If you encounter issues:
1. Check the Laravel logs in `storage/logs/`
2. Check browser console for JavaScript errors
3. Verify all services are running
4. Check database connectivity

$(document).ready(function() {
    let gameStatus = 'betting';
    let userBets = [];
    let totalUserBet = 0;
    
    // Initialize
    loadGameStatus();
    loadGameHistory();
    loadUserBets();
    
    // Auto-refresh every 5 seconds
    setInterval(() => {
        if (gameStatus === 'betting') {
            loadGameStatus();
            loadUserBets();
        }
    }, 5000);
    
    // Bet adjustment functions
    window.adjustBet = function(amount) {
        const currentBet = parseFloat($('.input__bet').val()) || 0;
        $('.input__bet').val(Math.max(1, currentBet + amount));
    };
    
    window.setBet = function(type) {
        if (type === 'min') {
            $('.input__bet').val(1);
        } else if (type === 'max') {
            const balance = parseFloat($('#balance').text()) || 0;
            $('.input__bet').val(Math.min(balance, 1000)); // Max bet limit
        }
    };
    
    // Place bet function
    window.placeBet = function(betType, betValue) {
        if (gameStatus !== 'betting') {
            showMessage('Ставки закрыты!', 'error');
            return;
        }
        
        const amount = parseFloat($('.input__bet').val());
        
        if (!amount || amount < 1) {
            showMessage('Введите корректную ставку', 'error');
            return;
        }
        
        $.post('/roulette/bet', {
            _token: $('meta[name="csrf-token"]').attr('content'),
            bet_type: betType,
            bet_value: betValue,
            amount: amount
        }).then(response => {
            if (response.type === 'success') {
                showMessage(response.msg, 'success');
                updateBalance(response.balance);
                loadUserBets();
                
                // Add visual feedback to the bet area
                highlightBetArea(betType, betValue);
            } else {
                showMessage(response.msg, 'error');
            }
        }).catch(error => {
            showMessage('Error placing bet', 'error');
        });
    };
    
    function loadGameStatus() {
        $.get('/roulette/status').then(response => {
            if (response.type === 'success') {
                if (response.game) {
                    gameStatus = response.game.status;
                    updateGameStatus(response.game);
                } else {
                    gameStatus = 'waiting';
                    $('#game-status').text('Waiting for new game...');
                }
            }
        });
    }
    
    function loadUserBets() {
        $.get('/roulette/user-bets').then(response => {
            if (response.type === 'success') {
                userBets = response.bets;
                displayUserBets(userBets);
                calculateTotalBet();
            }
        });
    }
    
    function loadGameHistory() {
        $.get('/roulette/history').then(response => {
            if (response.type === 'success') {
                displayGameHistory(response.history);
            }
        });
    }
    
    function updateGameStatus(game) {
        switch (game.status) {
            case 'betting':
                $('#game-status').text('Place your bets!');
                break;
            case 'spinning':
                $('#game-status').text('Wheel spinning...');
                spinWheel(game.winning_number);
                break;
            case 'finished':
                $('#game-status').text(`Result: ${game.winning_number}`);
                showWinningNumber(game.winning_number);
                setTimeout(() => {
                    loadGameStatus(); // Check for new game
                    loadGameHistory();
                }, 5000);
                break;
        }
    }
    
    function displayUserBets(bets) {
        const container = $('#user-bets-list');
        container.empty();
        
        bets.forEach(bet => {
            const betItem = `
                <div class="roulette__bet_item">
                    <div>${getBetDisplayName(bet.bet_type, bet.bet_value)}</div>
                    <div>${bet.amount} ₽ (${bet.payout_multiplier + 1}:1)</div>
                </div>
            `;
            container.append(betItem);
        });
    }
    
    function calculateTotalBet() {
        totalUserBet = userBets.reduce((total, bet) => total + parseFloat(bet.amount), 0);
        $('#total-user-bet').text(totalUserBet.toFixed(2));
    }
    
    function displayGameHistory(history) {
        const container = $('#game-history');
        container.empty();
        
        history.slice(0, 20).forEach(game => {
            const numberElement = `
                <div class="roulette__history_number ${game.color}">
                    ${game.number}
                </div>
            `;
            container.append(numberElement);
        });
    }
    
    function spinWheel(winningNumber) {
        const wheel = $('#roulette-wheel');
        const ball = $('#roulette-ball');
        
        // Calculate rotation for winning number
        const numberAngle = getNumberAngle(winningNumber);
        const spins = 5; // Number of full rotations
        const totalRotation = (spins * 360) + numberAngle;
        
        // Animate wheel
        wheel.css('transform', `rotate(${totalRotation}deg)`);
        ball.css('transform', `translateX(-50%) rotate(${-totalRotation}deg)`);
        
        // Show winning number after animation
        setTimeout(() => {
            showWinningNumber(winningNumber);
        }, 3000);
    }
    
    function getNumberAngle(number) {
        // European roulette wheel layout
        const wheelNumbers = [
            0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5,
            24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26
        ];
        
        const index = wheelNumbers.indexOf(number);
        return index * (360 / 37); // 37 numbers on European wheel
    }
    
    function showWinningNumber(number) {
        $('#winning-number').text(number);
        
        // Highlight winning number on table
        $('.roulette__number').removeClass('winning');
        $(`.roulette__number[data-number="${number}"]`).addClass('winning');
        
        // Check user wins
        checkUserWins(number);
    }
    
    function checkUserWins(winningNumber) {
        let totalWin = 0;
        
        userBets.forEach(bet => {
            if (isWinningBet(bet.bet_type, bet.bet_value, winningNumber)) {
                const winAmount = bet.amount * (bet.payout_multiplier + 1);
                totalWin += winAmount;
                
                showMessage(`Win: ${winAmount} ₽ (${getBetDisplayName(bet.bet_type, bet.bet_value)})`, 'win');
            }
        });
        
        if (totalWin > 0) {
            setTimeout(() => {
                updateBalance(parseFloat($('#balance').text()) + totalWin);
            }, 1000);
        }
        
        // Clear user bets for next round
        userBets = [];
        displayUserBets(userBets);
        calculateTotalBet();
    }
    
    function isWinningBet(betType, betValue, winningNumber) {
        switch (betType) {
            case 'straight':
                return parseInt(betValue) === winningNumber;
            case 'red':
                return getNumberColor(winningNumber) === 'red';
            case 'black':
                return getNumberColor(winningNumber) === 'black';
            case 'odd':
                return winningNumber > 0 && winningNumber % 2 === 1;
            case 'even':
                return winningNumber > 0 && winningNumber % 2 === 0;
            case 'low':
                return winningNumber >= 1 && winningNumber <= 18;
            case 'high':
                return winningNumber >= 19 && winningNumber <= 36;
            case 'first_dozen':
                return winningNumber >= 1 && winningNumber <= 12;
            case 'second_dozen':
                return winningNumber >= 13 && winningNumber <= 24;
            case 'third_dozen':
                return winningNumber >= 25 && winningNumber <= 36;
            default:
                return false;
        }
    }
    
    function getNumberColor(number) {
        if (number === 0) return 'green';
        const redNumbers = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36];
        return redNumbers.includes(number) ? 'red' : 'black';
    }
    
    function getBetDisplayName(betType, betValue) {
        const names = {
            'straight': `Number ${betValue}`,
            'red': 'Red',
            'black': 'Black',
            'odd': 'Odd',
            'even': 'Even',
            'low': '1-18',
            'high': '19-36',
            'first_dozen': '1st Dozen',
            'second_dozen': '2nd Dozen',
            'third_dozen': '3rd Dozen'
        };
        return names[betType] || betValue;
    }
    
    function highlightBetArea(betType, betValue) {
        // Add visual feedback when bet is placed
        let selector = '';
        
        if (betType === 'straight') {
            selector = `.roulette__number[data-number="${betValue}"]`;
        } else {
            selector = `.roulette__quick_btn.${betType}`;
        }
        
        $(selector).addClass('bet-placed');
        setTimeout(() => {
            $(selector).removeClass('bet-placed');
        }, 1000);
    }
    
    function showMessage(message, type) {
        // Create notification
        const notification = $(`
            <div class="roulette__notification ${type}">
                ${message}
            </div>
        `);
        
        $('body').append(notification);
        
        setTimeout(() => {
            notification.fadeOut(() => notification.remove());
        }, 3000);
    }
    
    function updateBalance(newBalance) {
        $('#balance').text(newBalance.toFixed(2));
    }
});

// Additional CSS for notifications and effects
$('<style>').text(`
    .roulette__notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        animation: slideIn 0.3s ease;
    }
    
    .roulette__notification.success { background: #28a745; }
    .roulette__notification.error { background: #dc3545; }
    .roulette__notification.win { background: #ffc107; color: #000; }
    
    @keyframes slideIn {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
    }
    
    .roulette__number.winning {
        animation: pulse 1s infinite;
        box-shadow: 0 0 20px #ffd700;
    }
    
    .bet-placed {
        animation: betPlaced 1s ease;
    }
    
    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }
    
    @keyframes betPlaced {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.2); background: #ffd700; }
    }
`).appendTo('head');

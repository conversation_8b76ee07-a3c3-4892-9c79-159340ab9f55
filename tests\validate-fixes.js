// Validation script to test all fixes
const fs = require('fs');
const path = require('path');

class FixValidator {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.passed = [];
    }

    // Test 1: Validate language settings
    testLanguageSettings() {
        console.log('Testing language settings...');
        
        // Check socket.js files
        const socketFiles = [
            'resources/js/socket.js',
            'public/assets/js/socket.js'
        ];

        socketFiles.forEach(file => {
            if (fs.existsSync(file)) {
                const content = fs.readFileSync(file, 'utf8');
                if (content.includes("const lang = 'en';")) {
                    this.passed.push(`✓ ${file}: Language set to English`);
                } else if (content.includes("const lang = 'ru';")) {
                    this.errors.push(`✗ ${file}: Language still set to Russian`);
                } else {
                    this.warnings.push(`⚠ ${file}: Language setting not found`);
                }
            } else {
                this.warnings.push(`⚠ ${file}: File not found`);
            }
        });
    }

    // Test 2: Check for remaining Russian text
    testRussianTextRemoval() {
        console.log('Testing Russian text removal...');
        
        const filesToCheck = [
            'resources/views/index.blade.php',
            'resources/views/layout.blade.php',
            'resources/js/socket.js',
            'resources/js/admin_app.js',
            'public/assets/js/socket.js',
            'public/assets/js/admin_app.js',
            'public/assets/js/roulette.js'
        ];

        const russianPatterns = [
            /Играть/g,
            /Ставка принята/g,
            /Произошла ошибка/g,
            /скопирован/g,
            /процессе/g,
            /Все права защищены/g,
            /Азартные игры/g,
            /Условия использования/g,
            /Политика конфиденциальности/g
        ];

        filesToCheck.forEach(file => {
            if (fs.existsSync(file)) {
                const content = fs.readFileSync(file, 'utf8');
                let foundRussian = false;
                
                russianPatterns.forEach(pattern => {
                    if (pattern.test(content)) {
                        foundRussian = true;
                    }
                });

                if (foundRussian) {
                    this.errors.push(`✗ ${file}: Still contains Russian text`);
                } else {
                    this.passed.push(`✓ ${file}: No Russian text found`);
                }
            } else {
                this.warnings.push(`⚠ ${file}: File not found`);
            }
        });
    }

    // Test 3: Validate socket connection configuration
    testSocketConfiguration() {
        console.log('Testing socket configuration...');
        
        const socketFiles = [
            'resources/js/socket.js',
            'public/assets/js/socket.js'
        ];

        socketFiles.forEach(file => {
            if (fs.existsSync(file)) {
                const content = fs.readFileSync(file, 'utf8');
                if (content.includes('window.location.hostname')) {
                    this.passed.push(`✓ ${file}: Dynamic socket connection configured`);
                } else if (content.includes('localhost:8443')) {
                    this.errors.push(`✗ ${file}: Still using hardcoded localhost`);
                } else {
                    this.warnings.push(`⚠ ${file}: Socket connection configuration unclear`);
                }
            }
        });
    }

    // Test 4: Check server configuration
    testServerConfiguration() {
        console.log('Testing server configuration...');
        
        const serverFile = 'server/app.js';
        if (fs.existsSync(serverFile)) {
            const content = fs.readFileSync(serverFile, 'utf8');
            
            if (content.includes('process.env.API_URL')) {
                this.passed.push(`✓ ${serverFile}: Dynamic API URL configured`);
            } else {
                this.errors.push(`✗ ${serverFile}: Still using hardcoded URLs`);
            }

            if (content.includes('origin: "*"')) {
                this.passed.push(`✓ ${serverFile}: CORS configured for development`);
            } else {
                this.warnings.push(`⚠ ${serverFile}: CORS configuration may be restrictive`);
            }
        } else {
            this.errors.push(`✗ ${serverFile}: Server file not found`);
        }
    }

    // Test 5: Validate environment configuration
    testEnvironmentConfig() {
        console.log('Testing environment configuration...');
        
        const envFile = '.env.example';
        if (fs.existsSync(envFile)) {
            const content = fs.readFileSync(envFile, 'utf8');
            
            if (content.includes('APP_NAME=Casino')) {
                this.passed.push(`✓ ${envFile}: App name set to Casino`);
            } else {
                this.warnings.push(`⚠ ${envFile}: App name not set to Casino`);
            }

            if (content.includes('SOCKET_PORT=8443')) {
                this.passed.push(`✓ ${envFile}: Socket port configured`);
            } else {
                this.warnings.push(`⚠ ${envFile}: Socket port not configured`);
            }
        } else {
            this.errors.push(`✗ ${envFile}: Environment example file not found`);
        }
    }

    // Test 6: Check development server
    testDevelopmentServer() {
        console.log('Testing development server...');
        
        const devServerFile = 'server/dev-server.js';
        if (fs.existsSync(devServerFile)) {
            this.passed.push(`✓ ${devServerFile}: Development server created`);
        } else {
            this.errors.push(`✗ ${devServerFile}: Development server not found`);
        }
    }

    // Run all tests
    runAllTests() {
        console.log('🧪 Running Casino Website Fix Validation Tests\n');
        
        this.testLanguageSettings();
        this.testRussianTextRemoval();
        this.testSocketConfiguration();
        this.testServerConfiguration();
        this.testEnvironmentConfig();
        this.testDevelopmentServer();
        
        this.printResults();
    }

    // Print test results
    printResults() {
        console.log('\n📊 Test Results Summary:');
        console.log('========================\n');
        
        if (this.passed.length > 0) {
            console.log('✅ PASSED TESTS:');
            this.passed.forEach(test => console.log(`  ${test}`));
            console.log('');
        }
        
        if (this.warnings.length > 0) {
            console.log('⚠️  WARNINGS:');
            this.warnings.forEach(warning => console.log(`  ${warning}`));
            console.log('');
        }
        
        if (this.errors.length > 0) {
            console.log('❌ FAILED TESTS:');
            this.errors.forEach(error => console.log(`  ${error}`));
            console.log('');
        }
        
        console.log(`📈 Summary: ${this.passed.length} passed, ${this.warnings.length} warnings, ${this.errors.length} errors`);
        
        if (this.errors.length === 0) {
            console.log('🎉 All critical tests passed! The fixes appear to be working correctly.');
        } else {
            console.log('🔧 Some issues found. Please review the errors above.');
        }
    }
}

// Run the validator
const validator = new FixValidator();
validator.runAllTests();

// Development server configuration for local testing
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const redis = require('redis');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"],
        allowedHeaders: ["X-Requested-With", "Content-Type"],
        credentials: true
    },
    serveClient: true,
    allowEIO3: true,
});

const PORT = process.env.PORT || 8443;
let online = 0;
let ipsConnected = [];

// Basic connection handling
io.on('connection', (socket) => {
    const address = socket.handshake.address;
    if (!ipsConnected.hasOwnProperty(address)) {
        ipsConnected[address] = 1;
        online = online + 1;
    }
    updateOnline(online);
    
    console.log('User connected:', address);
    
    socket.on('disconnect', () => {
        if (ipsConnected.hasOwnProperty(address)) {
            delete ipsConnected[address];
            online = online - 1;
        }
        updateOnline(online);
        console.log('User disconnected:', address);
    });
});

function updateOnline(online) {
    io.emit('live', { count: online });
}

// Simulate wheel game for development
let wheelTimer = 15;
let wheelActive = false;

function startWheelTimer() {
    if (wheelActive) return;
    
    wheelActive = true;
    wheelTimer = 15;
    
    const timer = setInterval(() => {
        if (wheelTimer > 0) {
            io.emit('wheel_start', wheelTimer);
            wheelTimer--;
        } else {
            clearInterval(timer);
            wheelActive = false;
            
            // Simulate wheel spin result
            const colors = ['red', 'black', 'yellow', 'green'];
            const result = colors[Math.floor(Math.random() * colors.length)];
            const rotation = Math.floor(Math.random() * 360);
            
            io.emit('wheel_roll', {
                timer: { data: 0 },
                roll: { data: rotation }
            });
            
            setTimeout(() => {
                io.emit('wheel_clear', {
                    clear: { data: 'clear_all' },
                    last: { data: result },
                    game: { id: Date.now() }
                });
                
                // Start next round
                setTimeout(startWheelTimer, 3000);
            }, 5000);
        }
    }, 1000);
}

// Start the wheel timer
startWheelTimer();

server.listen(PORT, () => {
    console.log(`Development server running on port ${PORT}`);
    console.log('Socket.io server ready for connections');
});

module.exports = { app, server, io };
